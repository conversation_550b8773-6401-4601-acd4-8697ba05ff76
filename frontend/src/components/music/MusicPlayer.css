/* Music Player Component - Material Design 3 */

.music-player {
  /* 真正的苹果毛玻璃效果 */
  background: rgba(255, 255, 255, 0.08);
  backdrop-filter: blur(60px) saturate(180%) brightness(1.05) contrast(1.1);
  -webkit-backdrop-filter: blur(60px) saturate(180%) brightness(1.05)
    contrast(1.1);

  /* 苹果式细腻边框 */
  border: 0.5px solid rgba(255, 255, 255, 0.2);

  /* 真实的苹果阴影系统 */
  box-shadow: 0 25px 50px -12px rgba(0, 0, 0, 0.25),
    0 8px 16px -8px rgba(0, 0, 0, 0.1), inset 0 1px 0 rgba(255, 255, 255, 0.15),
    inset 0 0 0 1px rgba(255, 255, 255, 0.05);

  width: 100%;
  max-width: 400px;
  border-radius: 16px;
  overflow: hidden;
  transition: all 0.4s cubic-bezier(0.25, 0.46, 0.45, 0.94);
  position: relative;

  /* 添加苹果式的背景噪点 */
  background-image: radial-gradient(
      circle at 25% 25%,
      rgba(255, 255, 255, 0.1) 0%,
      transparent 50%
    ),
    radial-gradient(
      circle at 75% 75%,
      rgba(255, 255, 255, 0.05) 0%,
      transparent 50%
    );
}

.music-player::before {
  content: "";
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  /* 苹果式顶部高光 */
  background: linear-gradient(
    180deg,
    rgba(255, 255, 255, 0.12) 0%,
    rgba(255, 255, 255, 0.06) 30%,
    rgba(255, 255, 255, 0.02) 60%,
    transparent 100%
  );
  pointer-events: none;
  z-index: 0;
  border-radius: 16px;
}

.music-player:hover {
  transform: translateY(-2px) scale(1.01);
  /* 悬停时增强毛玻璃效果 */
  backdrop-filter: blur(80px) saturate(200%) brightness(1.08) contrast(1.15);
  -webkit-backdrop-filter: blur(80px) saturate(200%) brightness(1.08)
    contrast(1.15);

  /* 悬停时的苹果式阴影 */
  box-shadow: 0 35px 70px -15px rgba(0, 0, 0, 0.3),
    0 12px 24px -8px rgba(0, 0, 0, 0.15), inset 0 1px 0 rgba(255, 255, 255, 0.2),
    inset 0 0 0 1px rgba(255, 255, 255, 0.08);

  /* 悬停时背景稍微增强 */
  background: rgba(255, 255, 255, 0.12);
  border-color: rgba(255, 255, 255, 0.25);
}

.music-player-content {
  padding: var(--md-sys-spacing-4);
  display: flex;
  flex-direction: column;
  gap: var(--md-sys-spacing-4);
  position: relative;
  z-index: 1;
}

/* Media Section */
.music-player-media {
  width: 100%;
  aspect-ratio: 1;
  border-radius: var(--md-sys-shape-corner-medium);
  overflow: hidden;
  box-shadow: var(--md-sys-elevation-level2);
}

.music-player-image {
  width: 100%;
  height: 100%;
  object-fit: cover;
  transition: transform var(--md-sys-motion-duration-medium2)
    var(--md-sys-motion-easing-standard);
}

.music-player:hover .music-player-image {
  transform: scale(1.02);
}

/* Info Section */
.music-player-info {
  display: flex;
  flex-direction: column;
  gap: var(--md-sys-spacing-3);
}

.music-player-header {
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
  gap: var(--md-sys-spacing-2);
}

.music-player-text {
  flex: 1;
  min-width: 0;
}

.music-player-title {
  color: rgba(255, 255, 255, 0.98);
  margin: 0 0 var(--md-sys-spacing-1);
  font-weight: 600;
  text-shadow: 0 1px 3px rgba(0, 0, 0, 0.2);
  letter-spacing: -0.01em;
}

.music-player-subtitle {
  color: rgba(255, 255, 255, 0.75);
  margin: 0 0 var(--md-sys-spacing-2);
  text-shadow: 0 1px 2px rgba(0, 0, 0, 0.15);
  font-weight: 400;
}

.music-player-artist {
  color: rgba(255, 255, 255, 0.95);
  margin: 0;
  font-weight: 500;
  text-shadow: 0 1px 3px rgba(0, 0, 0, 0.2);
  letter-spacing: -0.02em;
}

.music-player-like-btn {
  --md-icon-button-icon-color: rgba(255, 255, 255, 0.8);
  --md-icon-button-state-layer-color: rgba(255, 255, 255, 0.1);
  flex-shrink: 0;
  backdrop-filter: blur(10px);
  -webkit-backdrop-filter: blur(10px);
  background: rgba(255, 255, 255, 0.1);
  border-radius: 50%;
}

.music-player-like-btn[aria-label="Unlike"] {
  --md-icon-button-icon-color: #ff6b6b;
  background: rgba(255, 107, 107, 0.2);
}

/* Progress Section */
.music-player-progress {
  display: flex;
  flex-direction: column;
  gap: var(--md-sys-spacing-2);
}

.music-player-progress-bar {
  --md-linear-progress-active-indicator-color: rgba(255, 255, 255, 0.9);
  --md-linear-progress-track-color: rgba(255, 255, 255, 0.2);
  width: 100%;
  height: 4px;
  border-radius: 2px;
  backdrop-filter: blur(10px);
  -webkit-backdrop-filter: blur(10px);
}

.music-player-time {
  display: flex;
  justify-content: space-between;
  color: rgba(255, 255, 255, 0.7);
  text-shadow: 0 1px 2px rgba(0, 0, 0, 0.1);
}

/* Controls Section */
.music-player-controls {
  display: flex;
  align-items: center;
  justify-content: center;
  gap: var(--md-sys-spacing-2);
  padding: var(--md-sys-spacing-2) 0;
}

.music-player-controls md-icon-button {
  --md-icon-button-icon-color: rgba(255, 255, 255, 0.9);
  --md-icon-button-state-layer-color: rgba(255, 255, 255, 0.1);
  --md-icon-button-icon-size: 20px;
  background: transparent;
  border-radius: 50%;
  transition: all 0.2s ease;
}

/* Special styling for repeat and shuffle buttons (no background) */
.music-player-controls md-icon-button:first-child,
.music-player-controls md-icon-button:last-child {
  background: transparent;
  backdrop-filter: none;
  -webkit-backdrop-filter: none;
}

/* Special styling for prev/next buttons (no background) */
.music-player-controls md-icon-button:nth-child(2),
.music-player-controls md-icon-button:nth-child(4) {
  background: transparent;
  backdrop-filter: none;
  -webkit-backdrop-filter: none;
}

.music-player-play-btn {
  --md-filled-icon-button-container-color: rgba(255, 255, 255, 0.9);
  --md-filled-icon-button-icon-color: rgba(0, 0, 0, 0.8);
  --md-icon-button-icon-size: 28px;
  width: 56px;
  height: 56px;
  margin: 0 var(--md-sys-spacing-2);
  backdrop-filter: blur(40px) saturate(180%) brightness(1.1);
  -webkit-backdrop-filter: blur(40px) saturate(180%) brightness(1.1);
  box-shadow: 0 8px 25px rgba(0, 0, 0, 0.15), 0 3px 12px rgba(0, 0, 0, 0.1),
    inset 0 1px 0 rgba(255, 255, 255, 0.3);
  border: 0.5px solid rgba(255, 255, 255, 0.2);
}

.music-player-play-btn:hover {
  --md-filled-icon-button-container-color: rgba(255, 255, 255, 0.95);
  transform: scale(1.05);
  box-shadow: 0 12px 35px rgba(0, 0, 0, 0.2), 0 5px 15px rgba(0, 0, 0, 0.12),
    inset 0 1px 0 rgba(255, 255, 255, 0.4);
}

.music-player-play-btn:active {
  transform: scale(0.98);
}

/* Responsive Design */
@media (max-width: 768px) {
  .music-player {
    max-width: 100%;
  }

  .music-player-content {
    padding: var(--md-sys-spacing-3);
    gap: var(--md-sys-spacing-3);
  }

  .music-player-controls {
    gap: var(--md-sys-spacing-1);
  }

  .music-player-controls md-icon-button {
    --md-icon-button-icon-size: 18px;
  }

  .music-player-play-btn {
    --md-icon-button-icon-size: 24px;
    width: 48px;
    height: 48px;
  }
}

/* Animation for like button */
.music-player-like-btn md-icon {
  transition: transform var(--md-sys-motion-duration-short2)
    var(--md-sys-motion-easing-standard);
}

.music-player-like-btn:active md-icon {
  transform: scale(1.2);
}

/* Hover effects */
.music-player-controls md-icon-button:hover {
  --md-icon-button-state-layer-color: var(--md-sys-color-on-surface);
  --md-icon-button-state-layer-opacity: 0.08;
}

.music-player-like-btn:hover {
  --md-icon-button-state-layer-color: var(--md-sys-color-error);
  --md-icon-button-state-layer-opacity: 0.08;
}

/* Focus states */
.music-player-controls md-icon-button:focus-visible {
  outline: 2px solid var(--md-sys-color-primary);
  outline-offset: 2px;
}

/* Dark theme adjustments */
@media (prefers-color-scheme: dark) {
  .music-player {
    background: rgba(0, 0, 0, 0.4);
    border: 1px solid rgba(255, 255, 255, 0.1);
    box-shadow: 0 8px 32px 0 rgba(0, 0, 0, 0.5);
  }

  .music-player::before {
    background: linear-gradient(
      135deg,
      rgba(255, 255, 255, 0.05) 0%,
      rgba(255, 255, 255, 0.02) 100%
    );
  }

  .music-player:hover {
    border-color: rgba(255, 255, 255, 0.2);
    box-shadow: 0 12px 40px 0 rgba(0, 0, 0, 0.7);
  }

  .music-player-image {
    opacity: 0.9;
  }

  .music-player-title {
    color: rgba(255, 255, 255, 0.95);
  }

  .music-player-subtitle {
    color: rgba(255, 255, 255, 0.6);
  }

  .music-player-artist {
    color: rgba(255, 255, 255, 0.85);
  }

  .music-player-time {
    color: rgba(255, 255, 255, 0.6);
  }
}

/* Reduced motion */
@media (prefers-reduced-motion: reduce) {
  .music-player,
  .music-player-image,
  .music-player-like-btn md-icon,
  .music-player-play-btn {
    transition: none;
  }

  .music-player:hover {
    transform: none;
  }

  .music-player:hover .music-player-image {
    transform: none;
  }
}
