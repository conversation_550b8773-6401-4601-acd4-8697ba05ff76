/* Music Player Component - Material Design 3 */

.music-player {
  --md-elevated-card-container-color: var(--md-sys-color-surface-container-low);
  --md-elevated-card-container-elevation: var(--md-sys-elevation-level2);
  
  width: 100%;
  max-width: 400px;
  border-radius: var(--md-sys-shape-corner-large);
  overflow: hidden;
  transition: all var(--md-sys-motion-duration-medium2) var(--md-sys-motion-easing-standard);
}

.music-player:hover {
  --md-elevated-card-container-elevation: var(--md-sys-elevation-level3);
  transform: translateY(-2px);
}

.music-player-content {
  padding: var(--md-sys-spacing-4);
  display: flex;
  flex-direction: column;
  gap: var(--md-sys-spacing-4);
}

/* Media Section */
.music-player-media {
  width: 100%;
  aspect-ratio: 1;
  border-radius: var(--md-sys-shape-corner-medium);
  overflow: hidden;
  box-shadow: var(--md-sys-elevation-level2);
}

.music-player-image {
  width: 100%;
  height: 100%;
  object-fit: cover;
  transition: transform var(--md-sys-motion-duration-medium2) var(--md-sys-motion-easing-standard);
}

.music-player:hover .music-player-image {
  transform: scale(1.02);
}

/* Info Section */
.music-player-info {
  display: flex;
  flex-direction: column;
  gap: var(--md-sys-spacing-3);
}

.music-player-header {
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
  gap: var(--md-sys-spacing-2);
}

.music-player-text {
  flex: 1;
  min-width: 0;
}

.music-player-title {
  color: var(--md-sys-color-on-surface);
  margin: 0 0 var(--md-sys-spacing-1);
  font-weight: 600;
}

.music-player-subtitle {
  color: var(--md-sys-color-on-surface-variant);
  margin: 0 0 var(--md-sys-spacing-2);
}

.music-player-artist {
  color: var(--md-sys-color-on-surface);
  margin: 0;
  font-weight: 500;
}

.music-player-like-btn {
  --md-icon-button-icon-color: var(--md-sys-color-on-surface-variant);
  flex-shrink: 0;
}

.music-player-like-btn[aria-label="Unlike"] {
  --md-icon-button-icon-color: var(--md-sys-color-error);
}

/* Progress Section */
.music-player-progress {
  display: flex;
  flex-direction: column;
  gap: var(--md-sys-spacing-2);
}

.music-player-progress-bar {
  --md-linear-progress-active-indicator-color: var(--md-sys-color-primary);
  --md-linear-progress-track-color: var(--md-sys-color-surface-variant);
  width: 100%;
  height: 4px;
  border-radius: 2px;
}

.music-player-time {
  display: flex;
  justify-content: space-between;
  color: var(--md-sys-color-on-surface-variant);
}

/* Controls Section */
.music-player-controls {
  display: flex;
  align-items: center;
  justify-content: center;
  gap: var(--md-sys-spacing-2);
  padding: var(--md-sys-spacing-2) 0;
}

.music-player-controls md-icon-button {
  --md-icon-button-icon-color: var(--md-sys-color-on-surface-variant);
  --md-icon-button-icon-size: 20px;
}

.music-player-play-btn {
  --md-filled-icon-button-container-color: var(--md-sys-color-primary);
  --md-filled-icon-button-icon-color: var(--md-sys-color-on-primary);
  --md-icon-button-icon-size: 28px;
  width: 56px;
  height: 56px;
  margin: 0 var(--md-sys-spacing-2);
}

.music-player-play-btn:hover {
  --md-filled-icon-button-container-color: var(--md-sys-color-primary);
  transform: scale(1.05);
}

.music-player-play-btn:active {
  transform: scale(0.95);
}

/* Responsive Design */
@media (max-width: 768px) {
  .music-player {
    max-width: 100%;
  }
  
  .music-player-content {
    padding: var(--md-sys-spacing-3);
    gap: var(--md-sys-spacing-3);
  }
  
  .music-player-controls {
    gap: var(--md-sys-spacing-1);
  }
  
  .music-player-controls md-icon-button {
    --md-icon-button-icon-size: 18px;
  }
  
  .music-player-play-btn {
    --md-icon-button-icon-size: 24px;
    width: 48px;
    height: 48px;
  }
}

/* Animation for like button */
.music-player-like-btn md-icon {
  transition: transform var(--md-sys-motion-duration-short2) var(--md-sys-motion-easing-standard);
}

.music-player-like-btn:active md-icon {
  transform: scale(1.2);
}

/* Hover effects */
.music-player-controls md-icon-button:hover {
  --md-icon-button-state-layer-color: var(--md-sys-color-on-surface);
  --md-icon-button-state-layer-opacity: 0.08;
}

.music-player-like-btn:hover {
  --md-icon-button-state-layer-color: var(--md-sys-color-error);
  --md-icon-button-state-layer-opacity: 0.08;
}

/* Focus states */
.music-player-controls md-icon-button:focus-visible {
  outline: 2px solid var(--md-sys-color-primary);
  outline-offset: 2px;
}

/* Dark theme adjustments */
@media (prefers-color-scheme: dark) {
  .music-player {
    --md-elevated-card-container-color: var(--md-sys-color-surface-container);
  }
  
  .music-player-image {
    opacity: 0.9;
  }
}

/* Reduced motion */
@media (prefers-reduced-motion: reduce) {
  .music-player,
  .music-player-image,
  .music-player-like-btn md-icon,
  .music-player-play-btn {
    transition: none;
  }
  
  .music-player:hover {
    transform: none;
  }
  
  .music-player:hover .music-player-image {
    transform: none;
  }
}
