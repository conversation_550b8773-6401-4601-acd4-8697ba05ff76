import {Card, CardBody, Button, Image, Progress, CardProps} from "@heroui/react";
import {useState, useRef, useEffect, FC} from "react";
import {clsx} from "@heroui/shared-utils";
import {
  PlayIcon,
  PauseIcon,
  ForwardIcon,
  BackwardIcon,
  ArrowPathIcon,
  ArrowsRightLeftIcon,
  HeartIcon,
} from "@heroicons/react/24/solid";
import {HeartIcon as HeartOutlineIcon} from "@heroicons/react/24/outline";
import './HeroUIMusicPlayer.css';

// 音乐数据
const tracks = [
  {
    title: "Frontend Radio",
    artist: "Daily Mix",
    duration: "4:32",
    src: "/audio/track1.mp3"
  },
  {
    title: "React Vibes",
    artist: "Dev Playlist",
    duration: "3:45",
    src: "/audio/track2.mp3"
  },
  {
    title: "Code & Chill",
    artist: "Programming Mix",
    duration: "5:12",
    src: "/audio/track3.mp3"
  }
];

export interface MusicPlayerProps extends CardProps {}

export const MusicPlayer: FC<MusicPlayerProps> = ({className, ...otherProps}) => {
  const [liked, setLiked] = useState(false);
  const [isPlaying, setIsPlaying] = useState(false);
  const [currentTrack, setCurrentTrack] = useState(0);
  const [currentTime, setCurrentTime] = useState(0);
  const [duration, setDuration] = useState(0);
  const [isRepeating, setIsRepeating] = useState(false);
  const [isShuffling, setIsShuffling] = useState(false);
  const audioRef = useRef<HTMLAudioElement>(null);

  // 播放/暂停功能
  const togglePlay = () => {
    if (audioRef.current) {
      if (isPlaying) {
        audioRef.current.pause();
      } else {
        audioRef.current.play();
      }
      setIsPlaying(!isPlaying);
    }
  };

  // 切换到下一首
  const nextTrack = () => {
    setCurrentTrack((prev) => (prev + 1) % tracks.length);
  };

  // 切换到上一首
  const prevTrack = () => {
    setCurrentTrack((prev) => (prev - 1 + tracks.length) % tracks.length);
  };

  // 格式化时间
  const formatTime = (time: number) => {
    const minutes = Math.floor(time / 60);
    const seconds = Math.floor(time % 60);
    return `${minutes}:${seconds.toString().padStart(2, '0')}`;
  };

  // 音频事件处理
  useEffect(() => {
    const audio = audioRef.current;
    if (!audio) return;

    const handleTimeUpdate = () => setCurrentTime(audio.currentTime);
    const handleLoadedMetadata = () => setDuration(audio.duration);
    const handleEnded = () => {
      if (isRepeating) {
        audio.currentTime = 0;
        audio.play();
      } else {
        nextTrack();
      }
    };

    audio.addEventListener('timeupdate', handleTimeUpdate);
    audio.addEventListener('loadedmetadata', handleLoadedMetadata);
    audio.addEventListener('ended', handleEnded);

    return () => {
      audio.removeEventListener('timeupdate', handleTimeUpdate);
      audio.removeEventListener('loadedmetadata', handleLoadedMetadata);
      audio.removeEventListener('ended', handleEnded);
    };
  }, [currentTrack, isRepeating]);

  const progressValue = duration > 0 ? (currentTime / duration) * 100 : 0;

  return (
    <div className="relative w-full max-w-md mx-auto">
      {/* Apple-style Background Effects */}
      <div className="absolute inset-0 -z-10">
        {/* Dynamic gradient background based on current track */}
        <div
          className={clsx(
            "absolute inset-0 rounded-3xl opacity-80 blur-3xl transition-all duration-1000",
            currentTrack === 0 && "bg-gradient-to-br from-purple-500/40 via-pink-500/30 to-orange-400/40",
            currentTrack === 1 && "bg-gradient-to-br from-blue-500/40 via-cyan-500/30 to-teal-400/40",
            currentTrack === 2 && "bg-gradient-to-br from-green-500/40 via-emerald-500/30 to-yellow-400/40"
          )}
        />
        {/* Secondary blur layer */}
        <div className="absolute inset-2 bg-white/10 dark:bg-black/20 rounded-2xl backdrop-blur-xl" />
      </div>

      <Card
        isBlurred
        className={clsx(
          "border-none bg-white/25 dark:bg-black/15 backdrop-blur-2xl w-full",
          "shadow-2xl shadow-black/25 dark:shadow-black/50",
          "border border-white/30 dark:border-white/15",
          "music-player-card apple-glass-effect",
          className
        )}
        shadow="none"
        {...otherProps}
      >
      <CardBody className="p-6">
        <div className="grid grid-cols-6 md:grid-cols-12 gap-6 md:gap-4 items-center justify-center">
          <div className="relative col-span-6 md:col-span-4">
            {/* Album Cover with Apple-style shadow and reflection */}
            <div className="relative group">
              <div className="absolute inset-0 bg-gradient-to-t from-black/20 to-transparent rounded-2xl z-10" />
              <Image
                alt="Album cover"
                className="object-cover transition-transform duration-300 group-hover:scale-105 shadow-2xl shadow-black/40 rounded-2xl overflow-hidden"
                height={200}
                src="https://heroui.com/images/album-cover.png"
                width="100%"
              />
              {/* Reflection effect */}
              <div className="absolute -bottom-1 left-0 right-0 h-8 bg-gradient-to-t from-white/10 to-transparent rounded-b-2xl blur-sm" />
            </div>
          </div>

          <div className="flex flex-col col-span-6 md:col-span-8">
            <div className="flex justify-between items-start">
              <div className="flex flex-col gap-0">
                <h3 className="font-semibold text-foreground/90">Daily Mix</h3>
                <p className="text-sm text-foreground/80">{tracks.length} Tracks</p>
                <h1 className="text-lg font-medium mt-2">{tracks[currentTrack]?.title}</h1>
              </div>
              <Button
                isIconOnly
                className={clsx(
                  "text-default-900/60 data-[hover=true]:bg-foreground/10 -translate-y-2 translate-x-2",
                  "heart-button",
                  liked && "liked"
                )}
                radius="full"
                variant="light"
                onPress={() => setLiked((v) => !v)}
              >
                {liked ? (
                  <HeartIcon className="w-5 h-5 text-red-500" />
                ) : (
                  <HeartOutlineIcon
                    className="w-5 h-5"
                    fill="none"
                  />
                )}
              </Button>
            </div>

            <div className="music-player-progress flex flex-col mt-3 gap-1">
              <Progress
                aria-label="Music progress"
                classNames={{
                  indicator: "bg-default-800 dark:bg-white",
                  track: "bg-default-500/30",
                }}
                color="default"
                size="sm"
                value={progressValue}
              />
              <div className="flex justify-between">
                <p className="text-sm time-display">{formatTime(currentTime)}</p>
                <p className="text-sm time-display text-foreground/50">{tracks[currentTrack]?.duration}</p>
              </div>
            </div>

            <div className="music-player-controls flex w-full items-center justify-center gap-4">
              <Button
                isIconOnly
                className={clsx(
                  "data-[hover=true]:bg-foreground/10",
                  isRepeating && "text-primary"
                )}
                radius="full"
                variant="light"
                onPress={() => setIsRepeating(!isRepeating)}
              >
                <ArrowPathIcon className="w-5 h-5 text-foreground/80" />
              </Button>
              <Button
                isIconOnly
                className="data-[hover=true]:bg-foreground/10"
                radius="full"
                variant="light"
                onPress={prevTrack}
              >
                <BackwardIcon className="w-5 h-5" />
              </Button>
              <Button
                isIconOnly
                className="w-auto h-auto data-[hover=true]:bg-foreground/10 play-button"
                radius="full"
                variant="light"
                onPress={togglePlay}
              >
                {isPlaying ? (
                  <PauseIcon className="w-7 h-7" />
                ) : (
                  <PlayIcon className="w-7 h-7" />
                )}
              </Button>
              <Button
                isIconOnly
                className="data-[hover=true]:bg-foreground/10"
                radius="full"
                variant="light"
                onPress={nextTrack}
              >
                <ForwardIcon className="w-5 h-5" />
              </Button>
              <Button
                isIconOnly
                className={clsx(
                  "data-[hover=true]:bg-foreground/10",
                  isShuffling && "text-primary"
                )}
                radius="full"
                variant="light"
                onPress={() => setIsShuffling(!isShuffling)}
              >
                <ArrowsRightLeftIcon className="w-5 h-5 text-foreground/80" />
              </Button>
            </div>
          </div>
        </div>
      </CardBody>

      <audio
        ref={audioRef}
        src={tracks[currentTrack]?.src}
        preload="metadata"
      />
    </Card>
    </div>
  );
};
